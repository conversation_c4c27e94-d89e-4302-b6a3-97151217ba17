# For every file in folder /cm/archive/khanhpl2/V2A_Dataset/fieren/script/output/CFG4.5_euler_26gen_wav_16k_80_last/
# I accidentally add prefix "Y" and suffix "_9" to every file
# e.g., -2-wdcN5vOw_000017.wav -> Y-2-wdcN5vOw_000017_9.wav
# I want to remove them

# for file in /cm/archive/khanhpl2/V2A_Dataset/fieren/script/output/CFG4.5_euler_26gen_wav_16k_80_last/*; do
#     new_name=$(echo "$file" | sed 's/Y\(.*\)_9/\1/')
#     mv "$file" "$new_name"
# done

# Test with one sample only first, output to output_dir
output_dir="/cm/shared/thanhtvt/V2A-Evaluator/frieren_outputs"
for file in /cm/archive/khanhpl2/V2A_Dataset/fieren/script/output/CFG4.5_euler_26gen_wav_16k_80_last/*; do
    new_name=$(echo "$file" | sed 's/Y\(.*\)_9/\1/')
    cp "$file" "$output_dir/$(basename "$new_name")"
done