#!bin/bash
root_pred_cache="/cm/shared/thanhtvt/V2A-Evaluator/pred_cache/"
# pred_audio_path="/cm/shared/thanhtvt/MMAudio/output/train_vggsound_only/test-sampled"
pred_audio_path="/cm/shared/thanhtvt/V2A-Evaluator/frieren_outputs"
exp_name=$(basename "$pred_audio_path")

python evaluate.py  \
    --gt_audio "/cm/shared/sonnn45/Caption-generator/av-benchmark/tmp/2025-04-24_13-43-19/audio/gt" \
    --gt_cache "/cm/shared/sonnn45/Caption-generator/av-benchmark/gt_features" \
    --pred_audio "${pred_audio_path}" \
    --pred_cache "${root_pred_cache}/${exp_name}" \
    --audio_length=8.0 \
    --skip_clap

# ==================
# LanguageBind Score
# ==================

# echo "-------------------"
# echo "Evaluating LanguageBind score..."

# cd LanguageBind
# bash infer.sh 0 "${pred_audio_path}" 128