[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"
[project]
name = "laion_clap"
version = "1.1.6"
authors = [
  { name="<PERSON>", email="<EMAIL>" },
  { name="<PERSON><PERSON><PERSON>" },
  { name="<PERSON><PERSON><PERSON>" },
  { name="<PERSON><PERSON>" }
]
maintainers = [
  { name="<PERSON>", email="<EMAIL>" },
  { name="Yusong Wu" },
  { name="<PERSON><PERSON><PERSON>" },
  { name="<PERSON><PERSON>" }
]
description = "Contrastive Language-Audio Pretraining Model from LAION"
license = {file = "LICENSE"}
readme = "README.md"
requires-python = ">=3.7"
dependencies = [
  "numpy>=1.25",
  "soundfile",
  "librosa",
  "torchlibrosa",
  "ftfy",
  "braceexpand",
  "webdataset",
  "wget",
  "wandb",
  "llvmlite",
  "scipy",
  "scikit-learn",
  "pandas",
  "h5py",
  "tqdm",
  "regex",
  "transformers",
  "progressbar",
  'timm >= 1.0.12',
]
classifiers = [
    'Development Status :: 3 - Alpha',
    'Intended Audience :: Developers',
    'Intended Audience :: Science/Research',
    'License :: OSI Approved :: Apache Software License',
    'Topic :: Scientific/Engineering :: Artificial Intelligence',
]


[project.urls]
"Homepage" = "https://github.com/LAION-AI/CLAP"
"Bug Tracker" = "https://github.com/LAION-AI/CLAP/issues"