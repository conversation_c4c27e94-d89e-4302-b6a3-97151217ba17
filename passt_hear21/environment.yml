name: ba3l
channels:
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1
  - _openmp_mutex=4.5
  - _pytorch_select=0.1
  - appdirs=1.4.4
  - audioread=2.1.9
  - blas=1.0
  - brotlipy=0.7.0
  - bzip2=1.0.8
  - c-ares=1.17.1
  - ca-certificates=2020.12.5
  - cached-property=1.5.2
  - cached_property=1.5.2
  - certifi=2020.12.5
  - cffi=1.14.5
  - chardet=4.0.0
  - colorama=0.4.4
  - cryptography=3.4.6
  - cycler=0.10.0
  - decorator=4.4.2
  - docopt=0.6.2
  - ffmpeg=4.3.1
  - freetype=2.10.4
  - gettext=********
  - gitdb=4.0.5
  - gitpython=3.1.14
  - gmp=6.2.1
  - gnutls=3.6.13
  - h5py=3.1.0
  - hdf5=1.10.6
  - idna=2.10
  - importlib-metadata=3.7.3
  - importlib_metadata=3.7.3
  - intel-openmp=2020.2
  - joblib=1.0.1
  - jpeg=9d
  - jsonpickle=1.4.1
  - kiwisolver=1.3.1
  - krb5=1.17.2
  - lame=3.100
  - lcms2=2.12
  - ld_impl_linux-64=2.35.1
  - libblas=3.9.0
  - libcblas=3.9.0
  - libcurl=7.75.0
  - libedit=3.1.20191231
  - libev=4.33
  - libffi=3.3
  - libflac=1.3.3
  - libgcc-ng=9.3.0
  - libgfortran-ng=9.3.0
  - libgfortran5=9.3.0
  - libgomp=9.3.0
  - liblapack=3.9.0
  - libllvm10=10.0.1
  - libnghttp2=1.43.0
  - libogg=1.3.4
  - libopenblas=0.3.12
  - libopus=1.3.1
  - libpng=1.6.37
  - librosa=0.8.0
  - libsndfile=1.0.31
  - libssh2=1.9.0
  - libstdcxx-ng=9.3.0
  - libtiff=4.2.0
  - libvorbis=1.3.7
  - libwebp-base=1.2.0
  - llvm-openmp=11.1.0
  - llvmlite=0.36.0
  - lz4-c=1.9.3
  - matplotlib-base=3.3.4
  - mkl=2020.2
  - mkl-service=2.3.0
  - munch=2.5.0
  - ncurses=6.2
  - nettle=3.6
  - ninja=1.10.2
  - numba=0.53.0
  - numpy=1.20.1
  - olefile=0.46
  - openblas=0.3.12
  - openh264=2.1.1
  - openssl=1.1.1k
  - packaging=20.9
  - pandas=1.2.3
  - pillow=8.1.2
  - pip=21.0.1
  - pooch=1.3.0
  - py-cpuinfo=7.0.0
  - pycparser=2.20
  - pyopenssl=20.0.1
  - pyparsing=2.4.7
  - pysocks=1.7.1
  - pysoundfile=0.10.3.post1
  - python=3.7.10
  - python-dateutil=2.8.1
  - python_abi=3.7
  - pytz=2021.1
  - readline=8.0
  - requests=2.25.1
  - resampy=0.2.2
  - scikit-learn=0.24.1
  - scipy=1.6.1
  - setuptools=49.6.0
  - six=1.15.0
  - smmap=3.0.5
  - sqlite=3.34.0
  - threadpoolctl=2.1.0
  - tk=8.6.10
  - tornado=6.1
  - typing_extensions=*******
  - urllib3=1.26.4
  - wrapt=1.12.1
  - x264=1!161.3030
  - xz=5.2.5
  - zipp=3.4.1
  - zlib=1.2.11
  - zstd=1.4.9
  - pip:
    - absl-py==0.12.0
    - aiohttp==3.7.4.post0
    - async-timeout==3.0.1
    - attrs==20.3.0
    - av==8.0.3
    - black==20.8b1
    - cachetools==4.2.1
    - click==7.1.2
    - einops==0.3.0
    - fsspec==0.8.7
    - future==0.18.2
    - google-auth==1.28.0
    - google-auth-oauthlib==0.4.3
    - gpuinfo==1.0.0a7
    - grpcio==1.36.1
    - imageio==2.9.0
    - jedi==0.18.0
    - libtmux==0.8.5
    - markdown==3.3.4
    - multidict==5.1.0
    - mypy-extensions==0.4.3
    - oauthlib==3.1.0
    - parso==0.8.2
    - pathspec==0.8.1
    - prompt-toolkit==3.0.18
    - protobuf==3.15.6
    - ptpython==3.0.17
    - pyasn1==0.4.8
    - pyasn1-modules==0.2.8
    - pydub==0.25.1
    - pygments==2.8.1
    - pymongo==3.11.3
    - pyyaml==5.3.1
    - regex==2021.4.4
    - requests-oauthlib==1.3.0
    - rsa==4.7.2
    - tensorboard==2.4.1
    - tensorboard-plugin-wit==1.8.0
    - test-tube==0.7.5
    - timm==0.4.12
    - toml==0.10.2
    - torch==1.8.1+cu111
    - torchaudio==0.8.1
    - torchmetrics==0.2.0
    - torchvision==0.6.0
    - tqdm==4.59.0
    - typed-ast==1.4.3
    - wcwidth==0.2.5
    - werkzeug==1.0.1
    - wheel==0.36.2
    - yarl==1.6.3

