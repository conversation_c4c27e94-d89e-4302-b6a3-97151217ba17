import os

import pandas as pd

OPENAI_IMAGENET_TEMPLATES = (
    # lambda c: f'This is a sound of {c}.',
    lambda c: f'a sound of {c}.',
)
# OPENAI_IMAGENET_TEMPLATES = (
#     lambda c: f'a bad sound of a {c}.',
#     lambda c: f'a sound of many {c}.',
#     lambda c: f'a sculpture of a {c}.',
#     lambda c: f'a sound of the hard to see {c}.',
#     lambda c: f'a low resolution sound of the {c}.',
#     lambda c: f'a rendering of a {c}.',
#     lambda c: f'graffiti of a {c}.',
#     lambda c: f'a bad sound of the {c}.',
#     lambda c: f'a cropped sound of the {c}.',
#     lambda c: f'a tattoo of a {c}.',
#     lambda c: f'the embroidered {c}.',
#     lambda c: f'a sound of a hard to see {c}.',
#     lambda c: f'a bright sound of a {c}.',
#     lambda c: f'a sound of a clean {c}.',
#     lambda c: f'a sound of a dirty {c}.',
#     lambda c: f'a dark sound of the {c}.',
#     lambda c: f'a drawing of a {c}.',
#     lambda c: f'a sound of my {c}.',
#     lambda c: f'the plastic {c}.',
#     lambda c: f'a sound of the cool {c}.',
#     lambda c: f'a close-up sound of a {c}.',
#     lambda c: f'a black and white sound of the {c}.',
#     lambda c: f'a painting of the {c}.',
#     lambda c: f'a painting of a {c}.',
#     lambda c: f'a pixelated sound of the {c}.',
#     lambda c: f'a sculpture of the {c}.',
#     lambda c: f'a bright sound of the {c}.',
#     lambda c: f'a cropped sound of a {c}.',
#     lambda c: f'a plastic {c}.',
#     lambda c: f'a sound of the dirty {c}.',
#     lambda c: f'a jpeg corrupted sound of a {c}.',
#     lambda c: f'a blurry sound of the {c}.',
#     lambda c: f'a sound of the {c}.',
#     lambda c: f'a good sound of the {c}.',
#     lambda c: f'a rendering of the {c}.',
#     lambda c: f'a {c} in a video game.',
#     lambda c: f'a sound of one {c}.',
#     lambda c: f'a doodle of a {c}.',
#     lambda c: f'a close-up sound of the {c}.',
#     lambda c: f'a sound of a {c}.',
#     lambda c: f'the origami {c}.',
#     lambda c: f'the {c} in a video game.',
#     lambda c: f'a sketch of a {c}.',
#     lambda c: f'a doodle of the {c}.',
#     lambda c: f'a origami {c}.',
#     lambda c: f'a low resolution sound of a {c}.',
#     lambda c: f'the toy {c}.',
#     lambda c: f'a rendition of the {c}.',
#     lambda c: f'a sound of the clean {c}.',
#     lambda c: f'a sound of a large {c}.',
#     lambda c: f'a rendition of a {c}.',
#     lambda c: f'a sound of a nice {c}.',
#     lambda c: f'a sound of a weird {c}.',
#     lambda c: f'a blurry sound of a {c}.',
#     lambda c: f'a cartoon {c}.',
#     lambda c: f'art of a {c}.',
#     lambda c: f'a sketch of the {c}.',
#     lambda c: f'a embroidered {c}.',
#     lambda c: f'a pixelated sound of a {c}.',
#     lambda c: f'itap of the {c}.',
#     lambda c: f'a jpeg corrupted sound of the {c}.',
#     lambda c: f'a good sound of a {c}.',
#     lambda c: f'a plushie {c}.',
#     lambda c: f'a sound of the nice {c}.',
#     lambda c: f'a sound of the small {c}.',
#     lambda c: f'a sound of the weird {c}.',
#     lambda c: f'the cartoon {c}.',
#     lambda c: f'art of the {c}.',
#     lambda c: f'a drawing of the {c}.',
#     lambda c: f'a sound of the large {c}.',
#     lambda c: f'a black and white sound of a {c}.',
#     lambda c: f'the plushie {c}.',
#     lambda c: f'a dark sound of a {c}.',
#     lambda c: f'itap of a {c}.',
#     lambda c: f'graffiti of the {c}.',
#     lambda c: f'a toy {c}.',
#     lambda c: f'itap of my {c}.',
#     lambda c: f'a sound of a cool {c}.',
#     lambda c: f'a sound of a small {c}.',
#     lambda c: f'a tattoo of the {c}.',
# )

# a much smaller subset of above prompts
# from https://github.com/openai/CLIP/blob/main/notebooks/Prompt_Engineering_for_ImageNet.ipynb
SIMPLE_IMAGENET_TEMPLATES = (
    lambda c: f'itap of a {c}.',
    lambda c: f'a bad sound of the {c}.',
    lambda c: f'a origami {c}.',
    lambda c: f'a sound of the large {c}.',
    lambda c: f'a {c} in a video game.',
    lambda c: f'art of the {c}.',
    lambda c: f'a sound of the small {c}.',
)


PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "class_labels_indices.csv")


CLASSNAMES = {
    'Audioset': tuple(pd.read_csv(PATH).values[:, 2]),
    'ESC50': (
        'airplane', 'breathing', 'brushing teeth', 'can opening', 'car horn', 'cat', 'chainsaw', 'chirping birds',
        'church bells', 'clapping', 'clock alarm', 'clock tick', 'coughing', 'cow', 'crackling fire', 'crickets',
        'crow', 'crying baby', 'dog', 'door wood creaks', 'door wood knock', 'drinking sipping', 'engine', 'fireworks',
        'footsteps', 'frog', 'glass breaking', 'hand saw', 'helicopter', 'hen', 'insects', 'keyboard typing',
        'laughing', 'mouse click', 'pig', 'pouring water', 'rain', 'rooster', 'sea waves', 'sheep', 'siren',
        'sneezing', 'snoring', 'thunderstorm', 'toilet flush', 'train', 'vacuum cleaner', 'washing machine',
        'water drops', 'wind'
    ),
    'VGGSound': (
        'air conditioning noise', 'air horn', 'airplane', 'airplane flyby', 'alarm clock ringing',
        'alligators, crocodiles hissing', 'ambulance siren', 'arc welding', 'baby babbling', 'baby crying',
        'baby laughter', 'baltimore oriole calling', 'barn swallow calling', 'basketball bounce',
        'bathroom ventilation fan running', 'beat boxing', 'bee, wasp, etc. buzzing', 'bird chirping, tweeting',
        'bird squawking', 'bird wings flapping', 'black capped chickadee calling', 'blowtorch igniting',
        'bouncing on trampoline', 'bowling impact', 'bull bellowing', 'canary calling', 'cap gun shooting',
        'car engine idling', 'car engine knocking', 'car engine starting', 'car passing by', 'cat caterwauling',
        'cat growling', 'cat hissing', 'cat meowing', 'cat purring', 'cattle mooing', 'cattle, bovinae cowbell',
        'cell phone buzzing', 'chainsawing trees', 'cheetah chirrup', 'chicken clucking', 'chicken crowing',
        'child singing', 'child speech, kid speaking', 'children shouting', 'chimpanzee pant-hooting',
        'chinchilla barking', 'chipmunk chirping', 'chopping food', 'chopping wood', 'church bell ringing',
        'civil defense siren', 'cow lowing', 'coyote howling', 'cricket chirping', 'crow cawing', 'cuckoo bird calling',
        'cupboard opening or closing', 'cutting hair with electric trimmers', 'dinosaurs bellowing', 'disc scratching',
        'dog barking', 'dog baying', 'dog bow-wow', 'dog growling', 'dog howling', 'dog whimpering',
        'donkey, ass braying', 'door slamming', 'driving buses', 'driving motorcycle', 'driving snowmobile',
        'duck quacking', 'eagle screaming', 'eating with cutlery', 'electric grinder grinding',
        'electric shaver, electric razor shaving', 'elephant trumpeting', 'eletric blender running', 'elk bugling',
        'engine accelerating, revving, vroom', 'female singing', 'female speech, woman speaking', 'ferret dooking',
        'fire crackling', 'fire truck siren', 'fireworks banging', 'firing cannon', 'firing muskets',
        'fly, housefly buzzing', 'foghorn', 'footsteps on snow', 'forging swords', 'fox barking', 'francolin calling',
        'frog croaking', 'gibbon howling', 'goat bleating', 'golf driving', 'goose honking', 'hail',
        'hair dryer drying', 'hammering nails', 'heart sounds, heartbeat', 'hedge trimmer running', 'helicopter',
        'horse clip-clop', 'horse neighing', 'ice cracking', 'ice cream truck, ice cream van', 'lathe spinning',
        'lawn mowing', 'lighting firecrackers', 'lions growling', 'lions roaring', 'lip smacking',
        'machine gun shooting', 'magpie calling', 'male singing', 'male speech, man speaking', 'metronome',
        'missile launch', 'mosquito buzzing', 'motorboat, speedboat acceleration', 'mouse clicking', 'mouse pattering',
        'mouse squeaking', 'mynah bird singing', 'ocean burbling', 'opening or closing car doors',
        'opening or closing car electric windows', 'opening or closing drawers', 'orchestra', 'otter growling',
        'owl hooting', 'parrot talking', 'penguins braying', 'people babbling', 'people battle cry',
        'people belly laughing', 'people booing', 'people burping', 'people cheering', 'people clapping',
        'people coughing', 'people crowd', 'people eating', 'people eating apple', 'people eating crisps',
        'people eating noodle', 'people farting', 'people finger snapping', 'people gargling', 'people giggling',
        'people hiccup', 'people humming', 'people marching', 'people nose blowing', 'people running',
        'people screaming', 'people shuffling', 'people slapping', 'people slurping', 'people sneezing',
        'people sniggering', 'people sobbing', 'people whispering', 'people whistling', 'pheasant crowing',
        'pig oinking', 'pigeon, dove cooing', 'planing timber', 'plastic bottle crushing', 'playing accordion',
        'playing acoustic guitar', 'playing badminton', 'playing bagpipes', 'playing banjo', 'playing bass drum',
        'playing bass guitar', 'playing bassoon', 'playing bongo', 'playing bugle', 'playing castanets',
        'playing cello', 'playing clarinet', 'playing congas', 'playing cornet', 'playing cymbal', 'playing darts',
        'playing didgeridoo', 'playing djembe', 'playing double bass', 'playing drum kit', 'playing electric guitar',
        'playing electronic organ', 'playing erhu', 'playing flute', 'playing french horn', 'playing glockenspiel',
        'playing gong', 'playing guiro', 'playing hammond organ', 'playing harmonica', 'playing harp',
        'playing harpsichord', 'playing hockey', 'playing lacrosse', 'playing mandolin', 'playing marimba, xylophone',
        'playing oboe', 'playing piano', 'playing saxophone', 'playing shofar', 'playing sitar', 'playing snare drum',
        'playing squash', 'playing steel guitar, slide guitar', 'playing steelpan', 'playing synthesizer',
        'playing tabla', 'playing table tennis', 'playing tambourine', 'playing tennis', 'playing theremin',
        'playing timbales', 'playing timpani', 'playing trombone', 'playing trumpet', 'playing tuning fork',
        'playing tympani', 'playing ukulele', 'playing vibraphone', 'playing violin, fiddle', 'playing volleyball',
        'playing washboard', 'playing zither', 'police car (siren)', 'police radio chatter', 'popping popcorn',
        'printer printing', 'pumping water', 'race car, auto racing', 'railroad car, train wagon', 'raining', 'rapping',
        'reversing beeps', 'ripping paper', 'roller coaster running', 'rope skipping', 'rowboat, canoe, kayak rowing',
        'running electric fan', 'sailing', 'scuba diving', 'sea lion barking', 'sea waves', 'sharpen knife',
        'sheep bleating', 'shot football', 'singing bowl', 'singing choir', 'skateboarding', 'skidding', 'skiing',
        'sliding door', 'sloshing water', 'slot machine', 'smoke detector beeping', 'snake hissing', 'snake rattling',
        'splashing water', 'spraying water', 'squishing water', 'stream burbling', 'strike lighter', 'striking bowling',
        'striking pool', 'subway, metro, underground', 'swimming', 'tap dancing', 'tapping guitar',
        'telephone bell ringing', 'thunder', 'toilet flushing', 'tornado roaring', 'tractor digging', 'train horning',
        'train wheels squealing', 'train whistling', 'turkey gobbling', 'typing on computer keyboard',
        'typing on typewriter', 'underwater bubbling', 'using sewing machines', 'vacuum cleaner cleaning floors',
        'vehicle horn, car horn, honking', 'volcano explosion', 'warbler chirping', 'waterfall burbling',
        'whale calling', 'wind chime', 'wind noise', 'wind rustling leaves', 'wood thrush calling',
        'woodpecker pecking tree', 'writing on blackboard with chalk', 'yodelling', 'zebra braying'
    )

}
