#!bin/bash

device=$1
audio_dir=$2
batch_size=${3-128}
tsv_file="/cm/shared/thanhtvt/Flowley/dataset/VGGSound/memmap_fps8-dim768/test.tsv"
exp_name=$(basename "$audio_dir")
cache_file="/cm/shared/thanhtvt/V2A-Evaluator/LanguageBind/gt_video_features/lb_video_features.pth"
output_dir="/cm/shared/thanhtvt/V2A-Evaluator/pred_cache/${exp_name}"

# Print full command with all arguments
echo "Running command: python inference.py  \
    --device $device \
    --tsv_file $tsv_file \
    --audio_dir $audio_dir \
    --cache_file $cache_file \
    --output_dir $output_dir \
    --batch_size $batch_size"

python inference.py  \
    --device $device \
    --tsv_file $tsv_file \
    --audio_dir $audio_dir \
    --cache_file $cache_file \
    --output_dir $output_dir \
    --batch_size $batch_size