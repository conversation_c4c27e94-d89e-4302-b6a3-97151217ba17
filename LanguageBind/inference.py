import argparse
import pandas as pd
import torch
from tqdm import tqdm
from pathlib import Path
from languagebind import LanguageBind, to_device, transform_dict


def parse_args():
    parser = argparse.ArgumentParser(description='LanguageBind Inference')
    parser.add_argument('--device', type=str, default='0', help='Device to run the model on')
    parser.add_argument('--tsv_file', type=str, required=True,
                        default="/cm/shared/thanhtvt/Flowley/dataset/VGGSound/memmap_fps8-dim768/test.tsv",
                        help='Path to the TSV file containing test data')
    parser.add_argument('--audio_dir', type=str, help='Audio directory')
    parser.add_argument('--recompute_video_feat', action='store_true',
                        help='Recompute video features if set, otherwise use cached features')
    parser.add_argument('--cache_file', type=str, default='./cache', help='Cache file for video features')
    parser.add_argument('--output_dir', type=str, default='./output', help='Directory to save output results')
    parser.add_argument('--batch_size', type=int, default=128, help='Batch size for inference')
    return parser.parse_args()


def get_list_of_audio_files(audio_dir):
    audio_dir = Path(audio_dir)
    if not audio_dir.exists():
        raise ValueError(f"Audio directory {audio_dir} does not exist.")

    audio_files = list(audio_dir.glob('*.wav'))
    if not audio_files:
        raise ValueError(f"No audio files found in {audio_dir}.")

    return {audio_file.stem: str(audio_file) for audio_file in audio_files}


@torch.inference_mode()
def extract_and_save_video_features(modality_transform, video_files, cache_file):
    video_features = {}
    for video_file in tqdm(video_files, total=len(video_files), desc='Extracting video features'):
        inputs = {
            'video': to_device(modality_transform['video'](video_file), device),
        }
        embeddings = model(inputs)
        video_file = Path(video_file)
        video_features[video_file.stem] = embeddings['video'].cpu()

    print("Saving video features to {cache_file}.")
    cache_file = Path(cache_file)
    cache_file.parent.mkdir(parents=True, exist_ok=True)
    torch.save(video_features, cache_file)
    return video_features


def filter_dicts(audio_dict, video_dict):
    # Filter video_dict to only include keys that are also in audio_dict
    filtered_video_dict = {k: v for k, v in video_dict.items() if k in audio_dict}

    assert len(filtered_video_dict) == len(audio_dict), \
        "Mismatch in number of audio and video files after filtering."

    # Make both dictionaries have the same key order
    audio_keys = list(audio_dict.keys())
    video_dict = {k: filtered_video_dict[k] for k in audio_keys}
    audio_dict = {k: audio_dict[k] for k in audio_keys}
    return audio_dict, video_dict


if __name__ == '__main__':
    args = parse_args()
    device = torch.device(f'cuda:{args.device}' if torch.cuda.is_available() else 'cpu')
    cache_file = Path(args.cache_file)
    if args.recompute_video_feat or not cache_file.exists():
        clip_type = {
            'video': 'LanguageBind_Video_FT',  # also LanguageBind_Video
            'audio': 'LanguageBind_Audio_FT',  # also LanguageBind_Audio
        }
    else:
        clip_type = {'audio': 'LanguageBind_Audio_FT'}

    print("Loading model...")
    model = LanguageBind(clip_type=clip_type, cache_dir='./cache_dir')
    model = model.to(device)
    model.eval()
    print("Model loaded.")
    modality_transform = {c: transform_dict[c](model.modality_config[c]) for c in clip_type.keys()}

    if args.recompute_video_feat or not cache_file.exists():
        print("Extracting video features...")
        video_files = pd.read_csv(args.tsv_file, sep='\t')['path'].tolist()
        video_dict = extract_and_save_video_features(
            modality_transform, video_files, args.cache_file)
    else:
        video_dict = torch.load(args.cache_file)
        print(f"Load video features from {args.cache_file}.")

    audio_dict = get_list_of_audio_files(args.audio_dir)
    # Filtering
    audio_dict, video_dict = filter_dicts(audio_dict, video_dict)

    # Extract audio features
    print("Extracting audio features...")
    audio_files = list(audio_dict.values())
    batched_audio_files = [audio_files[i:i + args.batch_size] for i in range(0, len(audio_files), args.batch_size)]

    # Extract audio features by batch
    audio_embeddings = []
    with torch.inference_mode():
        for audio_batch in tqdm(batched_audio_files, total=len(batched_audio_files), desc='Extracting audio features'):
            audio_inputs = {
                'audio': to_device(modality_transform['audio'](audio_batch), device),
            }
            audio_embeds = model(audio_inputs)
            audio_embeddings.append(audio_embeds['audio'])
    # audio_inputs = {
    #     'audio': to_device(modality_transform['audio'](audio_files), device),
    # }
    # audio_embeddings = model(audio_inputs)

    print("Computing similarity...")
    audio_embeddings = torch.concat(audio_embeddings, dim=0)
    video_embeddings = torch.concat(list(video_dict.values()), dim=0).to(device)
    sim = torch.cosine_similarity(audio_embeddings, video_embeddings, dim=-1).mean().item()
    print("LanguageBind-Score:", sim)
    # Save score
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    output_file = output_dir / 'LanguageBind-Score.txt'
    with open(output_file, 'w') as f:
        f.write(f"LanguageBind-Score: {sim}\n")
    print(f"Score saved to {output_file}")
