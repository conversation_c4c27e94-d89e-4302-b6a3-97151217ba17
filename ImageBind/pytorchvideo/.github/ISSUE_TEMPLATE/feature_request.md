---
name: "\U0001F680 Feature Request"
about: Submit a proposal/request for a new PyTorchVideo feature

---

## 🚀 Feature
<!-- A clear and concise description of the feature proposal -->

NOTE: Please look at the existing list of Issues tagged with the label ['enhancement`](https://github.com/facebookresearch/pytorchvideo/issues?q=label%3Aenhancement). **Only open a new issue if you do not see your feature request there**.

## Motivation

<!-- Please outline the motivation for the proposal.
e.g. It would be great if I could do [...], I'm always frustrated when [...]. If this is related to another GitHub issue, please link here too -->

## Pitch

<!-- A clear and concise description, optionally with code examples showing the functionality you want. -->

NOTE: we only consider adding new features if they are useful for many users.
