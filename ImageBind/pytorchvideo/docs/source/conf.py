# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved

# flake8: noqa
import os
import sys

import unittest.mock as mock

# -- Project information -----------------------------------------------------
import pytorch_sphinx_theme
from recommonmark.parser import CommonMarkParser
from recommonmark.transform import AutoStructify


# -- Path setup --------------------------------------------------------------
sys.path.insert(0, os.path.abspath("../"))
sys.path.insert(0, os.path.abspath("../pytorchvideo"))
sys.path.insert(0, os.path.abspath("../../"))


# The full version, including alpha/beta/rc tags
try:
    import torch  # noqa
except ImportError:
    for m in [
        "torch",
        "torchvision",
        "torch.nn",
        "torch.autograd",
        "torch.autograd.function",
        "torch.nn.modules",
        "torch.nn.modules.utils",
        "torch.utils",
        "torch.utils.data",
        "torchvision",
        "torchvision.ops",
        "torchvision.datasets",
        "torchvision.datasets.folder",
        "torch.utils.data.IterableDataset",
    ]:
        sys.modules[m] = mock.Mock(name=m)


project = "PyTorchVideo"
copyright = "2021, PyTorchVideo contributors"
author = "PyTorchVideo contributors"


# -- General configuration ---------------------------------------------------

# If your documentation needs a minimal Sphinx version, state it here.
#
needs_sphinx = "3.0"

# Add any Sphinx extension module names here, as strings. They can be
# extensions coming with Sphinx (named 'sphinx.ext.*') or your custom
# ones.
extensions = [
    "recommonmark",
    "sphinx.ext.autodoc",
    "sphinx.ext.napoleon",
    "sphinx.ext.intersphinx",
    "sphinx.ext.todo",
    "sphinx.ext.coverage",
    "sphinx.ext.mathjax",
    "sphinx.ext.viewcode",
    "sphinx.ext.githubpages",
    "sphinx.ext.doctest",
    "sphinx.ext.ifconfig",
    "sphinx_markdown_tables",
]

# Add any paths that contain templates here, relative to this directory.
templates_path = ["_templates"]

# -- Configurations for plugins ------------
napoleon_google_docstring = True
napoleon_include_init_with_doc = True
napoleon_include_special_with_doc = True
napoleon_numpy_docstring = False
napoleon_use_rtype = False
autodoc_inherit_docstrings = False
autodoc_member_order = "bysource"

intersphinx_mapping = {
    "python": ("https://docs.python.org/3.6", None),
    "numpy": ("https://docs.scipy.org/doc/numpy/", None),
    "torch": ("https://pytorch.org/docs/master/", None),
}
# -------------------------

source_parsers = {".md": CommonMarkParser}

# Add any paths that contain templates here, relative to this directory.
# templates_path = ["_templates"]

# The suffix(es) of source filenames.
# You can specify multiple suffix as a list of string:
#
source_suffix = [".rst", ".md"]

# The master toctree document.
master_doc = "index"

# The language for content autogenerated by Sphinx. Refer to documentation
# for a list of supported languages.
#
# This is also used if you do content translation via gettext catalogs.
# Usually you set "language" from the command line for these cases.
language = None
autodoc_typehints = "description"
# List of patterns, relative to source directory, that match files and
# directories to ignore when looking for source files.
# This pattern also affects html_static_path and html_extra_path.
exclude_patterns = ["_build", "Thumbs.db", ".DS_Store", "build", "README.md"]

# The name of the Pygments (syntax highlighting) style to use.
pygments_style = "sphinx"

# If true, `todo` and `todoList` produce output, else they produce nothing.
todo_include_todos = True

# -- Options for HTML output -------------------------------------------------

# The theme to use for HTML and HTML Help pages.  See the documentation for
# a list of builtin themes.
#
html_theme = "pytorch_sphinx_theme"
html_theme_path = [pytorch_sphinx_theme.get_html_theme_path()]

# Theme options are theme-specific and customize the look and feel of a theme
# further.  For a list of options available for each theme, see the
# documentation.

html_theme_options = {
    "includehidden": False,
    "canonical_url": "https://pytorchvideo.org/api/",
    "pytorch_project": "docs",
}

html_baseurl = "/"

# Add any paths that contain custom static files (such as style sheets) here,
# relative to this directory. They are copied after the builtin static files,
# so a file named "default.css" will overwrite the builtin "default.css".

html_favicon = "../../website/website/static/img/favicon.png"

# -- Options for HTMLHelp output ------------------------------------------

# Output file base name for HTML help builder.
htmlhelp_basename = "pytorchvideodoc"

# -- Options for manual page output ---------------------------------------

# One entry per manual page. List of tuples
# (source start file, name, description, authors, manual section).
man_pages = [(master_doc, "pytorchvideo", "PyTorchVideo Documentation", [author], 1)]


# -- Options for Texinfo output -------------------------------------------

# Grouping the document tree into Texinfo files. List of tuples
# (source start file, target name, title, author,
#  dir menu entry, description, category)
texinfo_documents = [
    (
        master_doc,
        "PyTorchVideo",
        "PyTorchVideo Documentation",
        author,
        "PyTorchVideo",
        "One line description of project.",
        "Miscellaneous",
    )
]

github_doc_root = "https://github.com/facebookresearch/pytorchvideo/tree/main"


def setup(app):
    app.add_config_value(
        "recommonmark_config",
        {
            "url_resolver": lambda url: github_doc_root + url,
            "auto_toc_tree_section": "Contents",
        },
        True,
    )
    app.add_transform(AutoStructify)
