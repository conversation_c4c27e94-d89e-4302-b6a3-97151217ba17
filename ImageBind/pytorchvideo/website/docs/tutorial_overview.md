---
id: tutorial_overview
title: Tutorials
sidebar_label: Overview
---

PyTorchVideo tutorials are designed to help you get acquainted with the library and also give you an idea on how to incorporate different  PyTorchVideo components into your own video-research workflow. In the tutorials, through examples, we also show how PyTorchVideo makes it easy to address some of the common deeplearning video use cases.

PyTorchVideo is built on PyTorch. If you are new to PyTorch, the easiest way to get started is with the [PyTorch: A 60 Minute Blitz](https://pytorch.org/tutorials/beginner/blitz/tensor_tutorial.html#sphx-glr-beginner-blitz-tensor-tutorial-py) tutorial.
