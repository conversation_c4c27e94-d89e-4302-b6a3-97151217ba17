{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved."]}, {"cell_type": "markdown", "metadata": {"id": "yOVmvnwW6ism"}, "source": ["## Introduction\n", "Got your own model, but still want to fully leverage efficient blocks in PytorchVideo/Accelerator? No problem, model transmuter can help you.\n", "Model transmuter is a utility in PytorchVideo/Accelerator that takes user defined model, and replace modules in user model with equivalent efficient block when possible.\n", "In this tutorial, we will go through typical steps of using model transmuter, including:\n", "- Use model transmuter to replace modules in user model with efficient blocks\n", "- Convert model into deploy form and deploy\n", "\n", "Before we start, let's install PytorchVideo."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "2_v3ehr3Bt1T"}, "outputs": [], "source": ["!pip install pytorchvideo"]}, {"cell_type": "markdown", "metadata": {"id": "1-Rs<PERSON><PERSON><PERSON><PERSON>"}, "source": ["## Use model transmuter to replace modules in user model with efficient blocks\n", "First, let's assume user has following model to be transmuted:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ST7sgFdM6ist"}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "\n", "class user_model_residual_block(nn.Module):\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.stem0 = nn.Conv3d(3, 3, kernel_size=(3, 1, 1), padding=(1, 0, 0))\n", "        self.stem1 = nn.Conv3d(3, 3, kernel_size=(5, 1, 1), padding=(2, 0, 0))\n", "        self.pw = nn.Conv3d(3, 6, kernel_size=1)\n", "        self.relu = nn.ReLU()\n", "        self.dw = nn.Conv3d(6, 6, kernel_size=3, padding=1, groups=6)\n", "        self.relu1 = nn.ReLU()\n", "        self.pwl = nn.Conv3d(6, 3, kernel_size=1)\n", "        self.relu2 = nn.ReLU()\n", "\n", "    def forward(self, x):\n", "        out = self.stem0(x)\n", "        out = self.stem1(out)\n", "        out = self.pw(out)\n", "        out = self.relu(out)\n", "        out = self.dw(out)\n", "        out = self.relu1(out)\n", "        out = self.pwl(out)\n", "        return self.relu2(out + x)\n"]}, {"cell_type": "markdown", "metadata": {"id": "f6vbMoE46ist"}, "source": ["Then, let's use model transmuter by importing transmuter for targeting device. In this tutorial, we are using mobile cpu as example. Therefore we will import (1) model transmuter for mobile cpu and (2) top-level wrapper of model transmuter."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "zi8KsCSh6isu"}, "outputs": [], "source": ["import pytorchvideo.accelerator.deployment.mobile_cpu.transmuter  # mobile cpu model transmuter\n", "from pytorchvideo.accelerator.deployment.common.model_transmuter import transmute_model  # top-level wrapper of model transmuter"]}, {"cell_type": "markdown", "metadata": {"id": "t4meNp416isu"}, "source": ["We instantiate one user_model_residual_block, and transmute it by calling `transmute_model` with argument of `target_device=\"mobile_cpu\"`. We can see that the some of modules in model has been replaced by printing it again. In general, model transmuter will replace one submodule if its equivalent efficient block is found, otherwise that submodule will be kept intact."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "N-YzZp_d6isu"}, "outputs": [], "source": ["model_transmute = user_model_residual_block()\n", "print(\"original model\")\n", "print(model_transmute)\n", "transmute_model(\n", "    model_transmute,\n", "    target_device=\"mobile_cpu\",\n", ")\n", "print(\"after transmute\")\n", "print(model_transmute)"]}, {"cell_type": "markdown", "metadata": {"id": "eQi8UFdD6isv"}, "source": []}, {"cell_type": "markdown", "metadata": {"id": "74G3zWYF6isv"}, "source": ["## Convert model into deploy form and deploy\n", "Now the model is ready to deploy. First of all, let's convert the model into deploy form. In order to do that, we need to use `convert_to_deployable_form` utility and provide an example input tensor to the model. `convert_to_deployable_form` will convert any instance of `EfficientBlockBase` (base class for efficient blocks in PytorchVideo/Accelerator) into deploy form, while leave other modules unchanged.\n", "Note that once the model is converted into deploy form, the input size should be the same as the example input tensor size during conversion."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "NCeIb59m6isw"}, "outputs": [], "source": ["# Define example input tensor\n", "input_blob_size = (1, 3, 4, 6, 6)\n", "input_tensor = torch.randn(input_blob_size)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "3y3GBWdF6isw"}, "outputs": [], "source": ["from pytorchvideo.accelerator.deployment.mobile_cpu.utils.model_conversion import (\n", "    convert_to_deployable_form,\n", ")\n", "model_transmute_deploy = convert_to_deployable_form(\n", "    model_transmute, input_tensor\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "HLt0515O6isw"}, "source": ["We can observe further kernel graph change after conversion into deploy mode."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "7cd1NCew6isw"}, "outputs": [], "source": ["print(model_transmute_deploy)"]}, {"cell_type": "markdown", "metadata": {"id": "jCRJquGw6isx"}, "source": ["Currently model transmuter only supports fp32 operation, and it will support int8 with incoming torch.fx quantization mode. In this tutorial, we assume deploy transmuted model without quantization. In this case, all we need to do is to export jit trace and then apply `optimize_for_mobile` for final optimization."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "i2Mr_Il26isx"}, "outputs": [], "source": ["from torch.utils.mobile_optimizer import (\n", "    optimize_for_mobile,\n", ")\n", "traced_model = torch.jit.trace(model_transmute_deploy, input_tensor, strict=False)\n", "traced_model_opt = optimize_for_mobile(traced_model)\n", "# Here we can save the traced_model_opt to JIT file using traced_model_opt.save(<file_path>)"]}], "metadata": {"bento_stylesheets": {"bento/extensions/flow/main.css": true, "bento/extensions/kernel_selector/main.css": true, "bento/extensions/kernel_ui/main.css": true, "bento/extensions/new_kernel/main.css": true, "bento/extensions/system_usage/main.css": true, "bento/extensions/theme/main.css": true}, "colab": {"collapsed_sections": [], "name": "Use Model Transmuter.ipynb", "provenance": []}, "disseminate_notebook_id": {"notebook_id": "2903671383210410"}, "disseminate_notebook_info": {"bento_version": "20210321-210352", "description": "", "hide_code": false, "hipster_group": "", "kernel_build_info": {"error": ""}, "no_uii": true, "notebook_number": "520938", "others_can_edit": false, "reviewers": "", "revision_id": "464970858270301", "tags": "", "tasks": "", "title": "Use Model Transmuter"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.10"}}, "nbformat": 4, "nbformat_minor": 1}