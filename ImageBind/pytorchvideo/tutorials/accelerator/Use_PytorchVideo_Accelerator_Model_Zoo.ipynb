{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved."]}, {"cell_type": "markdown", "metadata": {"id": "PV1MwvbCm8X1"}, "source": ["## Introduction\n", "This tutorial goes through how to use model zoo provided by PytorchVideo/Accelerator. To use model zoo in PytorchVideo/Accelerator, we should generally follow several steps:\n", "- Use model builder to build selected model; \n", "- Load pretrain checkpoint;\n", "- (Optional) Finetune;\n", "- Deploy.\n", "\n", "Before we start, let's install PytorchVideo."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "h21XJwAKnB8q"}, "outputs": [], "source": ["!pip install pytorchvideo"]}, {"cell_type": "markdown", "metadata": {"id": "kppASAd8m8X4"}, "source": ["## Use model builder to build selected model\n", "We use model builder in PytorchVideo/Accelerator model zoo to build pre-defined efficient model. Here we use EfficientX3D-XS (for mobile_cpu) as an example. For more available models and details, please refer to [this page].\n", "\n", "EfficientX3D-XS is an implementation of X3D-XS network as described in [X3D paper](https://arxiv.org/abs/2004.04730) using efficient blocks. It is arithmetically equivalent with X3D-XS, but our benchmark on mobile phone shows 4.6X latency reduction compared with vanilla implementation.\n", "\n", "In order to build EfficientX3D-XS, we simply do the following:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "VwxiWAbQm8X5"}, "outputs": [], "source": ["from pytorchvideo.models.accelerator.mobile_cpu.efficient_x3d import EfficientX3d\n", "model_efficient_x3d_xs = EfficientX3d(expansion='XS', head_act='identity')"]}, {"cell_type": "markdown", "metadata": {"id": "uuRnwhYzm8X5"}, "source": ["Note that now the efficient blocks in the model are in original form, so the model is good for further training."]}, {"cell_type": "markdown", "metadata": {"id": "RSYnB3p8m8X5"}, "source": ["## Load pretrain checkpoint and (optional) finetune\n", "For each model in model zoo, we provide pretrain checkpoint state_dict for model in original form. See [this page] for details about checkpoints and where to download them."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "X9toVl9xm8X6"}, "outputs": [], "source": ["from torch.hub import load_state_dict_from_url\n", "checkpoint_path = 'https://dl.fbaipublicfiles.com/pytorchvideo/model_zoo/kinetics/efficient_x3d_xs_original_form.pyth'\n", "checkpoint = load_state_dict_from_url(checkpoint_path)\n", "\n", "model_efficient_x3d_xs.load_state_dict(checkpoint)"]}, {"cell_type": "markdown", "metadata": {"id": "cwPUPjJom8X6"}, "source": ["Now the model is ready for fine-tune. "]}, {"cell_type": "markdown", "metadata": {"id": "jcD6nyVzm8X6"}, "source": ["## Deploy\n", "Now the model is ready to deploy. First of all, let's convert the model into deploy form. In order to do that, we need to use `convert_to_deployable_form` utility and provide an example input tensor to the model. Note that once the model is converted into deploy form, the input size should be the same as the example input tensor size during conversion."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "2SAavQBZm8X7"}, "outputs": [], "source": ["import torch\n", "from pytorchvideo.accelerator.deployment.mobile_cpu.utils.model_conversion import (\n", "    convert_to_deployable_form,\n", ")\n", "input_blob_size = (1, 3, 4, 160, 160)\n", "input_tensor = torch.randn(input_blob_size)\n", "model_efficient_x3d_xs_deploy = convert_to_deployable_form(model_efficient_x3d_xs, input_tensor)"]}, {"cell_type": "markdown", "metadata": {"id": "ToAwX-2Jm8X7"}, "source": ["We can see that the network graph has been changed after conversion, which did kernel and graph optimization."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": true, "id": "EWMrKRpim8X7"}, "outputs": [], "source": ["print(model_efficient_x3d_xs_deploy)"]}, {"cell_type": "markdown", "metadata": {"id": "3HfFgDgCm8X8"}, "source": ["Next we have two options: either deploy floating point model, or quantize model into int8 and then deploy.\n", "\n", "Let's first assume we want to deploy floating point model. In this case, all we need to do is to export jit trace and then apply `optimize_for_mobile` for final optimization."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "966SbScHm8X9"}, "outputs": [], "source": ["from torch.utils.mobile_optimizer import (\n", "    optimize_for_mobile,\n", ")\n", "traced_model = torch.jit.trace(model_efficient_x3d_xs_deploy, input_tensor, strict=False)\n", "traced_model_opt = optimize_for_mobile(traced_model)\n", "# Here we can save the traced_model_opt to JIT file using traced_model_opt.save(<file_path>)"]}, {"cell_type": "markdown", "metadata": {"id": "Yjaeep9Wm8X9"}, "source": ["Alternatively, we may also want to deploy a quantized model. Efficient blocks are quantization-friendly by design - just wrap the model in deploy form with `QuantStub/DeQuantStub` and it is ready for Pytorch eager mode quantization."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-cD-OL4km8X9"}, "outputs": [], "source": ["import torch.nn as nn\n", "# Wrapper class for adding QuantStub/DeQuantStub.\n", "class quant_stub_wrapper(nn.Module):\n", "    def __init__(self, module_in):\n", "        super().__init__()\n", "        self.quant = torch.quantization.QuantStub()\n", "        self.model = module_in\n", "        self.dequant = torch.quantization.DeQuantStub()\n", "    def forward(self, x):\n", "        x = self.quant(x)\n", "        x = self.model(x)\n", "        x = self.dequant(x)\n", "        return x"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "b_-0Kyeym8X-"}, "outputs": [], "source": ["model_efficient_x3d_xs_deploy_quant_stub_wrapper = quant_stub_wrapper(model_efficient_x3d_xs_deploy)"]}, {"cell_type": "markdown", "metadata": {"id": "S_rv-Gxcm8YK"}, "source": ["Preparation step of quantization. Fusion has been done for efficient blocks automatically during `convert_to_deployable_form`, so we can just proceed to `torch.quantization.prepare`"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-kLtF7tpm8YL"}, "outputs": [], "source": ["model_efficient_x3d_xs_deploy_quant_stub_wrapper.qconfig = torch.quantization.default_qconfig\n", "model_efficient_x3d_xs_deploy_quant_stub_wrapper_prepared = torch.quantization.prepare(model_efficient_x3d_xs_deploy_quant_stub_wrapper)"]}, {"cell_type": "markdown", "metadata": {"id": "2W10VcNwm8YM"}, "source": ["Calibration and quantization. After preparation we will do calibration of quantization by feeding calibration dataset (skipped here) and then do quantization."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "zR2MrKv-m8YM"}, "outputs": [], "source": ["# calibration is skipped here.\n", "model_efficient_x3d_xs_deploy_quant_stub_wrapper_quantized = torch.quantization.convert(model_efficient_x3d_xs_deploy_quant_stub_wrapper_prepared)"]}, {"cell_type": "markdown", "metadata": {"id": "87eImwZCm8YM"}, "source": ["Then we can export trace of int8 model and deploy on mobile devices."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "kbN27xw_m8YM"}, "outputs": [], "source": ["traced_model_int8 = torch.jit.trace(model_efficient_x3d_xs_deploy_quant_stub_wrapper_quantized, input_tensor, strict=False)\n", "traced_model_int8_opt = optimize_for_mobile(traced_model_int8)\n", "# Here we can save the traced_model_opt to JIT file using traced_model_int8_opt.save(<file_path>)"]}], "metadata": {"bento_stylesheets": {"bento/extensions/flow/main.css": true, "bento/extensions/kernel_selector/main.css": true, "bento/extensions/kernel_ui/main.css": true, "bento/extensions/new_kernel/main.css": true, "bento/extensions/system_usage/main.css": true, "bento/extensions/theme/main.css": true}, "colab": {"collapsed_sections": [], "name": "Use PytorchVideo Accelerator Model Zoo.ipynb", "provenance": []}, "disseminate_notebook_id": {"notebook_id": "478609506614914"}, "disseminate_notebook_info": {"bento_version": "20210314-210430", "description": "", "hide_code": false, "hipster_group": "", "kernel_build_info": {"error": ""}, "no_uii": true, "notebook_number": "514048", "others_can_edit": false, "reviewers": "", "revision_id": "466653834533727", "tags": "", "tasks": "", "title": "Using PytorchVideo Accelerator Model Zoo"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.10"}}, "nbformat": 4, "nbformat_minor": 1}