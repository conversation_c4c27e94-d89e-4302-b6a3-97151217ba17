{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved."]}, {"cell_type": "markdown", "metadata": {"id": "DMh1bmnplgL7"}, "source": ["## Introduction\n", "In this tutorial, we will go through:\n", "- Basics of efficient blocks in PytorchVideo/Accelerator;\n", "- Design, train and deploy a model composed of efficient blocks for mobile CPU.\n", "\n", "Before we start, let's install PytorchVideo.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hh3uV620lrPO"}, "outputs": [], "source": ["!pip install pytorchvideo"]}, {"cell_type": "markdown", "metadata": {"id": "Vhh_AjMTlv8b"}, "source": []}, {"cell_type": "markdown", "metadata": {"id": "9QlnuONqlgMB"}, "source": ["## Basics of efficient blocks in PytorchVideo/Accelerator\n", "Efficient blocks are blocks with high efficiency. For a target device, we benchmark efficiency of basic network components and provide a collection of efficient blocks under `pytorchvideo/layers/accelerator/<target_device>` (for simple layers) and `pytorchvideo/models/accelerator/<target_device>` (for complex modules such as residual block). Inferencing of a model built up with corresponding efficient blocks on target device is guranteed to be efficient.\n", "\n", "Each efficient block module is an instance of nn.Module, and has two forms: **original form** (for training) and **deploy form** (for inference). When in original form, the efficient block module has exactly the same behavior as a corresponding vanilla nn.Module for both forward and backward operation. User can freely mix and match efficient blocks for the same target device and build up their own model. Once model is built and trained, user can convert each efficient block in model into deploy form. The conversion will do graph and kernel optimization on each efficient block, and efficient block in deploy form is arithmetically equivalent to original form but has much higher efficiency during inference. "]}, {"cell_type": "markdown", "metadata": {"id": "zrK_kLiClgMB"}, "source": ["## Design, train and deploy a model composed of efficient blocks for mobile CPU\n", "### Build a model\n", "In this section, let's go through the process of design, train and deploy using a example toy model using efficient blocks under `pytorchvideo/layers/accelerator/mobile_cpu` and `pytorchvideo/models/accelerator/mobile_cpu`, which includes:\n", "- One conv3d head layer with 5x1x1 kernel followed by ReLU activation;\n", "- One residual block with squeeze-excite;\n", "- One average pool and fully connected layer as final output.\n", "\n", "First, let's import efficient blocks."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "0jg4cZI5lgMC"}, "outputs": [], "source": ["# Imports\n", "import torch.nn as nn\n", "from pytorchvideo.layers.accelerator.mobile_cpu.activation_functions import (\n", "    supported_act_functions,\n", ")\n", "from pytorchvideo.layers.accelerator.mobile_cpu.convolutions import (\n", "    Conv3d5x1x1BnAct,\n", ")\n", "from pytorchvideo.models.accelerator.mobile_cpu.residual_blocks import (\n", "    X3dBottleneck<PERSON>lock,\n", ")\n", "from pytorchvideo.layers.accelerator.mobile_cpu.pool import AdaptiveAvgPool3dOutSize1\n", "from pytorchvideo.layers.accelerator.mobile_cpu.fully_connected import FullyConnected\n"]}, {"cell_type": "markdown", "metadata": {"id": "MxKCY8TzlgMC"}, "source": ["Then we can build a model using those efficient blocks."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "FYNnTanxlgMD"}, "outputs": [], "source": ["class MyNet(nn.Module):\n", "    def __init__(\n", "        self,\n", "        in_channel=3,  # input channel of first 5x1x1 layer\n", "        residual_block_channel=24,  # input channel of residual block\n", "        expansion_ratio=3, # expansion ratio of residual block\n", "        num_classes=4, # final output classes\n", "    ):\n", "        super().__init__()\n", "        # s1 - 5x1x1 conv3d layer\n", "        self.s1 = Conv3d5x1x1BnAct(\n", "            in_channel,\n", "            residual_block_channel,\n", "            bias=False,\n", "            groups=1,\n", "            use_bn=False,\n", "        )\n", "        # s2 - residual block\n", "        mid_channel = int(residual_block_channel * expansion_ratio)\n", "        self.s2 = X3dBottleneckBlock(\n", "                in_channels=residual_block_channel,\n", "                mid_channels=mid_channel,\n", "                out_channels=residual_block_channel,\n", "                use_residual=True,\n", "                spatial_stride=1,\n", "                se_ratio=0.0625,\n", "                act_functions=(\"relu\", \"swish\", \"relu\"),\n", "                use_bn=(True, True, True),\n", "            )\n", "        # Average pool and fully connected layer\n", "        self.avg_pool = AdaptiveAvgPool3dOutSize1()\n", "        self.projection = FullyConnected(residual_block_channel, num_classes, bias=True)\n", "        self.act = supported_act_functions['relu']()\n", "\n", "    def forward(self, x):\n", "        x = self.s1(x)\n", "        x = self.s2(x)\n", "        x = self.avg_pool(x)\n", "        # (N, C, T, H, W) -> (N, T, H, W, C).\n", "        x = x.permute((0, 2, 3, 4, 1))\n", "        x = self.projection(x)\n", "        # Performs fully convlutional inference.\n", "        if not self.training:\n", "            x = self.act(x)\n", "            x = x.mean([1, 2, 3])\n", "        x = x.view(x.shape[0], -1)\n", "\n", "        return x"]}, {"cell_type": "markdown", "metadata": {"id": "fB-_UEHilgMD"}, "source": ["We can instantiate MyNet and its efficient blocks will be in original form."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "FvXjdqT1lgMD"}, "outputs": [], "source": ["net_inst = MyNet()\n", "print(net_inst)"]}, {"cell_type": "markdown", "metadata": {"id": "-O6jd3umlgMF"}, "source": ["### Train model\n", "Then we can train the model with your dataset/optimizer. Here we skip this training step, and just leave the weight as initial value."]}, {"cell_type": "markdown", "metadata": {"id": "RdNV2EeMlgMF"}, "source": ["### Deploy model\n", "Now the model is ready to deploy. First of all, let's convert the model into deploy form. In order to do that, we need to use `convert_to_deployable_form` utility and provide an example input tensor to the model. Note that once the model is converted into deploy form, the input size should be the same as the example input tensor size during conversion."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "hA5ER4bLlgMF"}, "outputs": [], "source": ["import torch\n", "from pytorchvideo.accelerator.deployment.mobile_cpu.utils.model_conversion import (\n", "    convert_to_deployable_form,\n", ")\n", "input_blob_size = (1, 3, 4, 6, 6)\n", "input_tensor = torch.randn(input_blob_size)\n", "net_inst_deploy = convert_to_deployable_form(net_inst, input_tensor)\n"]}, {"cell_type": "markdown", "metadata": {"id": "6FC6knxWlgMG"}, "source": ["We can see that the network graph has been changed after conversion, which did kernel and graph optimization."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "WKXr2Pi1lgMG"}, "outputs": [], "source": ["print(net_inst_deploy)"]}, {"cell_type": "markdown", "metadata": {"id": "BlA-TZivlgMG"}, "source": ["Let's check whether the network after conversion is arithmetically equivalent. We expect the output to be very close before/after conversion, with some small difference due to numeric noise from floating point operation."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "I8lsM5oulgMG"}, "outputs": [], "source": ["net_inst.eval()\n", "out_ref = net_inst(input_tensor)\n", "out = net_inst_deploy(input_tensor)\n", "\n", "max_err = float(torch.max(torch.abs(out_ref - out)))\n", "print(f\"max error is {max_err}\")"]}, {"cell_type": "markdown", "metadata": {"id": "Yq4c5HeYlgMH"}, "source": ["Next we have two options: either deploy floating point model, or quantize model into int8 and then deploy.\n", "\n", "Let's first assume we want to deploy floating point model. In this case, all we need to do is to export jit trace and then apply `optimize_for_mobile` for final optimization."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ZPX9InColgMH"}, "outputs": [], "source": ["from torch.utils.mobile_optimizer import (\n", "    optimize_for_mobile,\n", ")\n", "traced_model = torch.jit.trace(net_inst_deploy, input_tensor, strict=False)\n", "traced_model_opt = optimize_for_mobile(traced_model)\n", "# Here we can save the traced_model_opt to JIT file using traced_model_opt.save(<file_path>)"]}, {"cell_type": "markdown", "metadata": {"id": "6jFmLo-algMI"}, "source": ["Alternatively, we may also want to deploy a quantized model. Efficient blocks are quantization-friendly by design - just wrap the model in deploy form with `QuantStub/DeQuantStub` and it is ready for Pytorch eager mode quantization."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "syb-6y2glgMI"}, "outputs": [], "source": ["# Wrapper class for adding QuantStub/DeQuantStub.\n", "class quant_stub_wrapper(nn.Module):\n", "    def __init__(self, module_in):\n", "        super().__init__()\n", "        self.quant = torch.quantization.QuantStub()\n", "        self.model = module_in\n", "        self.dequant = torch.quantization.DeQuantStub()\n", "    def forward(self, x):\n", "        x = self.quant(x)\n", "        x = self.model(x)\n", "        x = self.dequant(x)\n", "        return x"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "yUrtbvo_lgMI"}, "outputs": [], "source": ["net_inst_quant_stub_wrapper = quant_stub_wrapper(net_inst_deploy)"]}, {"cell_type": "markdown", "metadata": {"id": "qEX2FdcIlgMI"}, "source": ["Preparation step of quantization. Fusion has been done for efficient blocks automatically during `convert_to_deployable_form`, so we can just proceed to `torch.quantization.prepare`"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "r6DfTh1ElgMI"}, "outputs": [], "source": ["net_inst_quant_stub_wrapper.qconfig = torch.quantization.default_qconfig\n", "net_inst_quant_stub_wrapper_prepared = torch.quantization.prepare(net_inst_quant_stub_wrapper)"]}, {"cell_type": "markdown", "metadata": {"id": "q-SkDlVflgMJ"}, "source": ["Calibration and quantization. After preparation we will do calibration of quantization by feeding calibration dataset (skipped here) and then do quantization."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "E9Zh45yalgMJ"}, "outputs": [], "source": ["# calibration is skipped here.\n", "net_inst_quant_stub_wrapper_quantized = torch.quantization.convert(net_inst_quant_stub_wrapper_prepared)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "n1j11-5KlgMJ"}, "outputs": [], "source": ["print(net_inst_quant_stub_wrapper_quantized)"]}, {"cell_type": "markdown", "metadata": {"id": "7KjWPclrlgMJ"}, "source": ["Then we can export trace of int8 model and deploy on mobile devices."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "D5YXI4kvlgMK"}, "outputs": [], "source": ["traced_model_int8 = torch.jit.trace(net_inst_quant_stub_wrapper_quantized, input_tensor, strict=False)\n", "traced_model_int8_opt = optimize_for_mobile(traced_model_int8)\n", "# Here we can save the traced_model_opt to JIT file using traced_model_int8_opt.save(<file_path>)"]}], "metadata": {"bento_stylesheets": {"bento/extensions/flow/main.css": true, "bento/extensions/kernel_selector/main.css": true, "bento/extensions/kernel_ui/main.css": true, "bento/extensions/new_kernel/main.css": true, "bento/extensions/system_usage/main.css": true, "bento/extensions/theme/main.css": true}, "colab": {"collapsed_sections": [], "name": "Build your model with PytorchVideo Accelerator.ipynb", "provenance": []}, "disseminate_notebook_id": {"notebook_id": "709466976415887"}, "disseminate_notebook_info": {"bento_version": "20210314-210430", "description": "PTV tutorial", "hide_code": false, "hipster_group": "", "kernel_build_info": {"error": ""}, "no_uii": true, "notebook_number": "512478", "others_can_edit": false, "reviewers": "", "revision_id": "482523946213747", "tags": "", "tasks": "", "title": "Build your model with PytorchVideo Accelerator"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.10"}}, "nbformat": 4, "nbformat_minor": 1}