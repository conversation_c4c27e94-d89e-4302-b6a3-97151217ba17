[tool.poetry]
name = "kadtk"
version = "1.1.0"
description = "A toolkit library for Kernel Audio Distance."
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"]
readme = "README.md"
homepage = "https://github.com/YoonjinXD/kadtk"
repository = "https://github.com/YoonjinXD/kadtk"

[tool.poetry.dependencies]
python = ">=3.9,<3.12"
hypy-utils = "^1.0.19"
wheel = "^0.41.1"
numba = "^0.58.0"
librosa = "^0.10.1"
encodec = "^0.1.1"
nnaudio = "^0.3.2"
resampy = "^0.4.2"
hear21passt = "^0.0.26"
tensorflow = ">=2.0.0"
kapre = ">=0.3.5"

[tool.poetry.scripts]
kadtk = "kadtk.__main__:main"
kadtk-embeds = "kadtk.embeds:main"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
